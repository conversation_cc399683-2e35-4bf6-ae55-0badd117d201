#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第二阶段解密脚本 - 处理多层混淆的批处理文件
"""

import re
import argparse
import sys

def extract_decrypted_content(file_path):
    """从解密文件中提取实际的混淆内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找解密后的内容部分
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if '7B"' in line and 'C2*)n_4v' in line:
                # 找到混淆内容的开始
                obfuscated_content = line.strip()
                print(f"找到混淆内容，长度: {len(obfuscated_content)}")
                return obfuscated_content
        
        print("未找到混淆内容")
        return None
        
    except Exception as e:
        print(f"读取文件失败: {e}")
        return None

def analyze_pattern(content):
    """分析混淆模式"""
    print("分析混淆模式...")
    
    # 查找可能的字符替换模式
    patterns = []
    
    # 查找重复的字符序列
    char_freq = {}
    for char in content:
        char_freq[char] = char_freq.get(char, 0) + 1
    
    # 按频率排序
    sorted_chars = sorted(char_freq.items(), key=lambda x: x[1], reverse=True)
    print("字符频率分析（前20个）:")
    for char, freq in sorted_chars[:20]:
        if char.isprintable() and char != ' ':
            print(f"  '{char}': {freq}")
    
    # 查找可能的分隔符
    possible_separators = []
    for char, freq in sorted_chars:
        if not char.isalnum() and char in content and freq > 10:
            possible_separators.append(char)
    
    print(f"可能的分隔符: {possible_separators[:10]}")
    
    return sorted_chars, possible_separators

def try_simple_substitution(content):
    """尝试简单的字符替换解密"""
    print("尝试简单字符替换...")
    
    # 常见的字符替换模式
    substitutions = [
        # 数字替换
        {'0': 'o', '1': 'i', '3': 'e', '4': 'a', '5': 's', '7': 't', '8': 'b', '9': 'g'},
        # 符号替换
        {'*': ' ', '+': 'n', '-': 'r', '_': 'l', '=': 'e', '?': 'h'},
        # 混合替换
        {'W': 'w', 'B': 'b', 'C': 'c', 'D': 'd', 'F': 'f', 'G': 'g', 'H': 'h'},
    ]
    
    results = []
    
    for i, sub_dict in enumerate(substitutions):
        result = content
        for old_char, new_char in sub_dict.items():
            result = result.replace(old_char, new_char)
        
        print(f"替换方案 {i+1} 结果预览: {result[:100]}...")
        results.append(result)
    
    return results

def try_reverse_operations(content):
    """尝试反向操作"""
    print("尝试反向操作...")
    
    operations = []
    
    # 1. 反转字符串
    reversed_content = content[::-1]
    operations.append(("反转", reversed_content))
    
    # 2. 移除特定字符
    cleaned_content = re.sub(r'[^a-zA-Z0-9\s\'"@%\[\](){}.,;:!?\\/-]', '', content)
    operations.append(("清理特殊字符", cleaned_content))
    
    # 3. 提取可能的命令
    commands = re.findall(r'[a-zA-Z]{3,}', content)
    if commands:
        operations.append(("提取命令", ' '.join(commands[:20])))
    
    for name, result in operations:
        print(f"{name} 结果预览: {result[:100]}...")
    
    return operations

def try_base64_like_decode(content):
    """尝试类似Base64的解码"""
    print("尝试Base64类解码...")
    
    # 查找可能的Base64字符串
    base64_pattern = r'[A-Za-z0-9+/=]{20,}'
    matches = re.findall(base64_pattern, content)
    
    if matches:
        print(f"找到 {len(matches)} 个可能的Base64字符串")
        for i, match in enumerate(matches[:5]):
            print(f"  匹配 {i+1}: {match[:50]}...")
            try:
                import base64
                decoded = base64.b64decode(match + '==')
                print(f"    解码结果: {decoded[:50]}...")
            except:
                print(f"    解码失败")
    
    return matches

def extract_meaningful_strings(content):
    """提取有意义的字符串"""
    print("提取有意义的字符串...")
    
    # 查找可能的文件路径
    paths = re.findall(r'[A-Za-z]:\\[^"<>|*?\n]*', content)
    if paths:
        print("找到可能的文件路径:")
        for path in paths[:5]:
            print(f"  {path}")
    
    # 查找可能的URL
    urls = re.findall(r'https?://[^\s"<>]+', content)
    if urls:
        print("找到可能的URL:")
        for url in urls[:5]:
            print(f"  {url}")
    
    # 查找可能的命令
    commands = re.findall(r'\b(echo|set|call|start|cmd|powershell|wget|curl|download)\b', content, re.IGNORECASE)
    if commands:
        print(f"找到可能的命令: {set(commands)}")
    
    # 查找可能的注册表项
    registry = re.findall(r'HKEY_[A-Z_]+\\[^"<>\n]*', content)
    if registry:
        print("找到可能的注册表项:")
        for reg in registry[:3]:
            print(f"  {reg}")
    
    return {
        'paths': paths,
        'urls': urls, 
        'commands': commands,
        'registry': registry
    }

def main():
    parser = argparse.ArgumentParser(description='第二阶段解密脚本')
    parser.add_argument('input_file', help='输入的解密文件')
    parser.add_argument('-o', '--output', help='输出文件名', default='stage2_decrypted.txt')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("第二阶段解密分析")
    print("=" * 60)
    
    # 提取混淆内容
    content = extract_decrypted_content(args.input_file)
    if not content:
        print("无法提取混淆内容")
        return
    
    print(f"混淆内容长度: {len(content)}")
    print(f"内容预览: {content[:200]}...")
    print("-" * 60)
    
    # 分析模式
    char_freq, separators = analyze_pattern(content)
    print("-" * 60)
    
    # 尝试各种解密方法
    substitution_results = try_simple_substitution(content)
    print("-" * 60)
    
    reverse_results = try_reverse_operations(content)
    print("-" * 60)
    
    base64_matches = try_base64_like_decode(content)
    print("-" * 60)
    
    meaningful_strings = extract_meaningful_strings(content)
    print("-" * 60)
    
    # 保存分析结果
    with open(args.output, 'w', encoding='utf-8') as f:
        f.write("第二阶段解密分析结果\n")
        f.write("=" * 60 + "\n\n")
        
        f.write("原始混淆内容:\n")
        f.write(content + "\n\n")
        
        f.write("字符频率分析:\n")
        for char, freq in char_freq[:30]:
            if char.isprintable():
                f.write(f"'{char}': {freq}\n")
        f.write("\n")
        
        f.write("字符替换尝试:\n")
        for i, result in enumerate(substitution_results):
            f.write(f"方案 {i+1}:\n{result}\n\n")
        
        f.write("反向操作尝试:\n")
        for name, result in reverse_results:
            f.write(f"{name}:\n{result}\n\n")
        
        f.write("有意义的字符串:\n")
        for key, values in meaningful_strings.items():
            if values:
                f.write(f"{key}: {values}\n")
        f.write("\n")
    
    print(f"分析结果已保存到: {args.output}")

if __name__ == "__main__":
    main()
