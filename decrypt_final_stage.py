﻿# -*- coding: utf-8 -*-
import re
import argparse
import sys

def deobfuscate_batch_script(file_path):
    """
    读取并解密一个使用特定字符串替换方法混淆的批处理文件。

    :param file_path: 待解密的.bat文件路径
    """
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
    except FileNotFoundError:
        print(f"错误：文件未找到 -> {file_path}", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"读取文件时发生错误: {e}", file=sys.stderr)
        sys.exit(1)

    # 1. 提取字符映射表 (the long string in set '=...)
    # 使用正则表达式查找以 "set '=" 开头，且包含大量字符的行
    char_map_match = re.search(r"set \'\=(?P<map>.*)", content, re.IGNORECASE | re.DOTALL)
    if not char_map_match:
        print("错误：未能在文件中找到字符映射表 (set '=...)", file=sys.stderr)
        return
    
    # 原始的字符映射表，移除可能存在的前后换行符
    char_map = char_map_match.group('map').strip()
    # 在这个特定的脚本中，变量定义被分成了多行，我们需要将它们合并
    char_map = "".join(char_map.splitlines())


    # 2. 提取主要的混淆echo命令
    # 查找以 echo " 开始，并包含大量 %':~...% 结构的部分
    obfuscated_echo_match = re.search(r'echo "\s(?P<payload>.*)"', content, re.IGNORECASE | re.DOTALL)
    if not obfuscated_echo_match:
        print("错误：未能在文件中找到混淆的 echo 命令。", file=sys.stderr)
        return
        
    payload = obfuscated_echo_match.group('payload')

    # 3. 定义一个函数，用于根据偏移量从映射表中查找字符
    def replacer(match):
        """
        这个函数被 re.sub 调用，对每一个匹配到的占位符进行处理
        """
        try:
            # 提取括号内的数字（偏移量）
            offset = int(match.group(1))
            # 从字符映射表中返回对应位置的字符
            return char_map[offset]
        except IndexError:
            # 如果偏移量超出范围，返回一个错误标记
            return f"[OFFSET_ERROR:{match.group(1)}]"
        except Exception:
            return f"[UNKNOWN_ERROR:{match.group(0)}]"

    # 4. 开始解码
    # 使用正则表达式查找所有 %':~<offset>,1% 形式的占位符
    # (-?\d+) 匹配一个可选的负号和一位或多位数字
    decoded_payload = re.sub(r"%\':~(-?\d+),1%", replacer, payload)
    
    # 5. 处理其他特殊占位符
    # a. 处理 !'! 这种用于输出 ! 的情况
    decoded_payload = decoded_payload.replace("!'!'", "!")
    # b. 处理 %0% 这种代表脚本自身文件名的情况
    decoded_payload = decoded_payload.replace("%0%", "[SCRIPT_FILENAME]") # 脚本中是 %0
    decoded_payload = decoded_payload.replace("0 ", "[SCRIPT_FILENAME] ") # 脚本中也用了 '0 '

    # 6. 输出最终的明文结果
    print("--- [!] 警告：以下是解密后的恶意代码明文，请勿执行！ ---")
    print("\n")
    print(decoded_payload)
    print("\n")
    print("--- [!] 解密完成。请在安全的文本编辑器中分析以上内容。 ---")


if __name__ == '__main__':
    # --- 设置命令行参数解析 ---
    parser = argparse.ArgumentParser(
        description="解密一个特定类型的混淆批处理(.bat)脚本。",
        epilog="示例用法: python deobfuscate_bat.py infected.bat > decoded_payload.txt"
    )
    parser.add_argument(
        "input_file",
        help="需要解密的混淆.bat文件的路径"
    )
    
    args = parser.parse_args()
    
    deobfuscate_batch_script(args.input_file)