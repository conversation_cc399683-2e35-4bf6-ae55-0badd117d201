# -*- coding: utf-8 -*-
import re
import argparse
import sys

def deobfuscate_batch_script(file_path):
    """
    读取并解密一个使用特定字符串替换方法混淆的批处理文件。

    :param file_path: 待解密的.bat文件路径
    """
    try:
        # 尝试不同的编码方式读取文件
        encodings = ['utf-8', 'gbk', 'cp1252', 'latin1']
        content = None
        used_encoding = None

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                    used_encoding = encoding
                    break
            except UnicodeDecodeError:
                continue

        if content is None:
            # 如果所有编码都失败，使用二进制模式读取
            with open(file_path, 'rb') as f:
                raw_content = f.read()
                content = raw_content.decode('utf-8', errors='replace')
                used_encoding = 'utf-8 (with errors replaced)'

    except FileNotFoundError:
        print(f"错误：文件未找到 -> {file_path}")
        sys.exit(1)
    except Exception as e:
        print(f"读取文件时发生错误: {e}")
        sys.exit(1)

    print(f"Analyzing file: {file_path}")
    print(f"Using encoding: {used_encoding}")
    print("=" * 60)

    # 1. 提取字符映射表
    # 查找包含字符映射表的行，通常在第3-4行
    lines = content.splitlines()
    char_map = ""

    # 寻找字符映射表，通常以 "set '=" 开头且包含大量字符
    for i, line in enumerate(lines):
        if line.startswith("set '=") and len(line) > 20:  # 增加长度要求
            # 提取等号后的内容
            map_part = line[6:]  # 跳过 "set '="
            char_map += map_part

            # 检查下一行是否是映射表的延续（不以set或echo开头的行）
            if i + 1 < len(lines):
                next_line = lines[i + 1].strip()
                print(f"Checking next line: '{next_line}'")
                if (not next_line.startswith("set ") and
                    not next_line.startswith("echo") and
                    not next_line.startswith("setlocal") and
                    not next_line.startswith("endlocal") and
                    len(next_line) > 5):
                    char_map += next_line
                    print(f"Added continuation line: {next_line}")
            break

    if not char_map:
        print("Error: Could not find character mapping table")
        return

    print(f"Found character mapping table, length: {len(char_map)}")
    print(f"Mapping content: {char_map}")
    print("-" * 60)


    # 2. 提取主要的混淆echo命令
    # 查找包含大量 %':~...% 结构的echo命令
    payload = ""

    for i, line in enumerate(lines):
        if 'echo "' in line and "%':~" in line:
            # 提取echo命令中的内容，处理可能的多行情况
            start_pos = line.find('echo "') + 6  # 跳过 'echo "'
            if start_pos > 5:  # 确保找到了 'echo "'
                # 由于这是一个很长的行，我们需要找到整个内容
                # 这个文件的echo命令可能没有结束引号在同一行
                payload = line[start_pos:]  # 取从 'echo "' 之后的所有内容
                break

    if not payload:
        print("Error: Could not find obfuscated echo command")
        return

    print(f"Found obfuscated echo command, length: {len(payload)}")
    print(f"Command start: {payload[:100]}..." if len(payload) > 100 else f"Command content: {payload}")
    print("-" * 60)

    # 3. 定义一个函数，用于根据偏移量从映射表中查找字符
    def replacer(match):
        """
        这个函数被 re.sub 调用，对每一个匹配到的占位符进行处理
        """
        try:
            # 提取括号内的数字（偏移量）
            offset = int(match.group(1))
            # 从字符映射表中返回对应位置的字符
            # 处理所有偏移量，使用模运算确保在有效范围内
            actual_offset = offset % len(char_map)

            # 如果是负偏移量，从末尾开始计算
            if offset < 0:
                actual_offset = len(char_map) + (offset % len(char_map))
                if actual_offset >= len(char_map):
                    actual_offset = actual_offset % len(char_map)

            return char_map[actual_offset]
        except (IndexError, ValueError):
            # 如果偏移量超出范围或无法转换为整数，返回一个错误标记
            return f"[OFFSET_ERROR:{match.group(1)}]"
        except Exception:
            return f"[UNKNOWN_ERROR:{match.group(0)}]"

    print("Starting decoding...")

    # 4. 开始解码
    # 使用正则表达式查找所有 %':~<offset>,1% 形式的占位符
    # (-?\d+) 匹配一个可选的负号和一位或多位数字
    decoded_payload = re.sub(r"%\':~(-?\d+),1%", replacer, payload)

    # 5. 处理其他特殊占位符
    # a. 处理 !'! 这种用于输出 ! 的情况
    decoded_payload = decoded_payload.replace("!'!'", "!")
    # b. 处理 %0% 这种代表脚本自身文件名的情况
    decoded_payload = decoded_payload.replace("%0%", "[SCRIPT_FILENAME]")
    # c. 处理单独的 '0 ' 这种情况
    decoded_payload = re.sub(r'\b0\s+', "[SCRIPT_FILENAME] ", decoded_payload)

    # 6. 检查解码结果中是否还有未解码的占位符
    remaining_placeholders = re.findall(r"%\':~(-?\d+),1%", decoded_payload)
    if remaining_placeholders:
        print(f"警告：发现 {len(remaining_placeholders)} 个未解码的占位符")
        print(f"未解码的偏移量: {remaining_placeholders[:10]}...")  # 只显示前10个

    # 7. 统计解码信息
    error_count = len(re.findall(r"\[.*?ERROR:.*?\]", decoded_payload))
    if error_count > 0:
        print(f"警告：解码过程中发现 {error_count} 个错误")

    print("=" * 60)
    print("解码完成！")
    print("=" * 60)

    # 8. 输出最终的明文结果
    print("\n--- [!] 警告：以下是解密后的恶意代码明文，请勿执行！ ---\n")
    print(decoded_payload)
    print("\n--- [!] 解密完成。请在安全的文本编辑器中分析以上内容。 ---")

    # 9. 可选：将结果保存到文件
    output_file = file_path.replace('.bat', '_decrypted.txt')
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("解密后的内容：\n")
            f.write("=" * 60 + "\n")
            f.write(decoded_payload)
            f.write("\n" + "=" * 60 + "\n")
            f.write("解密完成\n")
        print(f"\n解密结果已保存到: {output_file}")
    except Exception as e:
        print(f"保存文件时出错: {e}")


if __name__ == '__main__':
    # --- 设置命令行参数解析 ---
    parser = argparse.ArgumentParser(
        description="解密一个特定类型的混淆批处理(.bat)脚本。",
        epilog="示例用法: python decrypt_final_stage.py Ins_improved_decrypted.bat"
    )
    parser.add_argument(
        "input_file",
        nargs='?',
        default="Ins_improved_decrypted.bat",
        help="需要解密的混淆.bat文件的路径（默认: Ins_improved_decrypted.bat）"
    )
    parser.add_argument(
        "-v", "--verbose",
        action="store_true",
        help="显示详细的解码过程信息"
    )

    args = parser.parse_args()

    print("批处理文件解密工具")
    print("=" * 60)

    deobfuscate_batch_script(args.input_file)