#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终解密脚本 - 基于所有分析结果的综合解密
"""

import re
import argparse
import sys

def extract_original_content():
    """从第二阶段文件中读取原始混淆内容"""
    try:
        with open('stage2_decrypted.txt', 'r', encoding='utf-8') as f:
            content = f.read()

        # 查找原始混淆内容部分
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if line.strip() == "原始混淆内容:":
                if i + 1 < len(lines):
                    return lines[i + 1].strip()

        return None

    except Exception as e:
        print(f"读取文件失败: {e}")
        # 如果文件不存在，返回一个简化的测试内容
        return "7B\"W4w t`C2*)n_4v<!'!<261<tK4Q_\"F/s-9%[SCRIPT_FILENAME] test content"

def try_comprehensive_decode(content):
    """尝试综合解密方法"""
    print("尝试综合解密方法...")
    
    results = []
    
    # 方法1: 基于观察到的模式 - 这看起来像是一个字符替换密码
    # 从分析中我们看到很多重复的模式，特别是 %[SCRIPT_FILENAME]
    
    # 尝试将一些明显的混淆字符替换回去
    method1 = content
    
    # 替换明显的占位符
    method1 = method1.replace('%[SCRIPT_FILENAME]', '%0')
    
    # 尝试一些常见的字符替换
    replacements = {
        'w': 'o',  # 基于频率分析
        'W': 'O',
        '4': 'a',
        '7': 't',
        '1': 'i',
        '5': 's',
        '6': 'g',
        '8': 'b',
        '9': 'g',
        '0': 'o',
        '2': 'z',
        '3': 'e',
    }
    
    for old, new in replacements.items():
        method1 = method1.replace(old, new)
    
    results.append(("字符替换法", method1))
    
    # 方法2: 尝试反向字符串
    method2 = content[::-1]
    results.append(("反向字符串", method2))
    
    # 方法3: 尝试移除重复字符
    method3 = re.sub(r'(.)\1+', r'\1', content)
    results.append(("去重复字符", method3))
    
    # 方法4: 尝试提取可能的Base64内容
    base64_pattern = r'[A-Za-z0-9+/=]{20,}'
    base64_matches = re.findall(base64_pattern, content)
    if base64_matches:
        method4 = '\n'.join(base64_matches[:10])
        results.append(("Base64提取", method4))
    
    # 方法5: 尝试Caesar密码 (ROT13等)
    def caesar_decode(text, shift):
        result = ""
        for char in text:
            if char.isalpha():
                base = ord('A') if char.isupper() else ord('a')
                result += chr((ord(char) - base - shift) % 26 + base)
            else:
                result += char
        return result
    
    method5 = caesar_decode(content, 13)  # ROT13
    results.append(("ROT13解密", method5))
    
    # 方法6: 尝试交替字符提取
    even_chars = content[::2]
    odd_chars = content[1::2]
    results.append(("偶数位字符", even_chars))
    results.append(("奇数位字符", odd_chars))
    
    return results

def search_for_clear_text(content):
    """搜索明文内容"""
    print("搜索可能的明文内容...")
    
    # 查找可能的命令和关键词
    patterns = [
        r'echo\s+[^&\n]*',
        r'set\s+[^&\n]*',
        r'call\s+[^&\n]*',
        r'start\s+[^&\n]*',
        r'cmd\s+[^&\n]*',
        r'powershell\s+[^&\n]*',
        r'http[s]?://[^\s"<>]+',
        r'[A-Za-z]:\\[^\s"<>|*?\n]*',
        r'HKEY_[A-Z_]+\\[^\s"<>\n]*',
    ]
    
    found_items = []
    for pattern in patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)
        if matches:
            found_items.extend([(pattern, match) for match in matches[:5]])
    
    return found_items

def main():
    print("=" * 60)
    print("最终综合解密尝试")
    print("=" * 60)
    
    # 获取原始内容
    content = extract_original_content()
    print(f"原始内容长度: {len(content)}")
    print(f"内容预览: {content[:200]}...")
    print("-" * 60)
    
    # 尝试各种解密方法
    decode_results = try_comprehensive_decode(content)
    
    # 搜索明文内容
    clear_text_items = search_for_clear_text(content)
    
    # 保存所有结果
    with open('final_decrypt_results.txt', 'w', encoding='utf-8') as f:
        f.write("最终综合解密结果\n")
        f.write("=" * 60 + "\n\n")
        
        f.write("原始混淆内容:\n")
        f.write(content + "\n\n")
        
        f.write("各种解密方法尝试:\n")
        f.write("-" * 40 + "\n")
        for method_name, result in decode_results:
            f.write(f"\n{method_name}:\n")
            f.write(result[:1000] + "...\n")  # 限制长度
        
        f.write("\n\n找到的可能明文内容:\n")
        f.write("-" * 40 + "\n")
        for pattern, match in clear_text_items:
            f.write(f"模式 {pattern}: {match}\n")
    
    print("所有解密尝试结果已保存到: final_decrypt_results.txt")
    
    # 显示一些关键发现
    print("\n关键发现:")
    print("-" * 30)
    
    # 显示字符替换法的结果预览
    if decode_results:
        char_replace_result = decode_results[0][1]
        print(f"字符替换法结果预览: {char_replace_result[:200]}...")
    
    # 显示找到的明文内容
    if clear_text_items:
        print("\n找到的可能明文:")
        for pattern, match in clear_text_items[:5]:
            print(f"  {match}")
    else:
        print("未找到明显的明文内容")

if __name__ == "__main__":
    main()
