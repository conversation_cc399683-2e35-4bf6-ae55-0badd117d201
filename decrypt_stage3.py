#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第三阶段解密脚本 - 基于模式分析的深度解密
"""

import re
import argparse
import sys

def extract_content_from_stage2(file_path):
    """从第二阶段文件中提取原始混淆内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找原始混淆内容部分
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if line.strip() == "原始混淆内容:":
                if i + 1 < len(lines):
                    return lines[i + 1].strip()
        
        return None
        
    except Exception as e:
        print(f"读取文件失败: {e}")
        return None

def analyze_repeating_patterns(content):
    """分析重复模式"""
    print("分析重复模式...")
    
    # 查找重复的字符序列
    patterns = {}
    
    # 查找长度为2-10的重复模式
    for length in range(2, 11):
        for i in range(len(content) - length):
            pattern = content[i:i+length]
            if pattern in patterns:
                patterns[pattern] += 1
            else:
                patterns[pattern] = 1
    
    # 过滤出现频率高的模式
    frequent_patterns = {k: v for k, v in patterns.items() if v > 5 and len(k) > 2}
    
    print("高频重复模式:")
    for pattern, count in sorted(frequent_patterns.items(), key=lambda x: x[1], reverse=True)[:10]:
        print(f"  '{pattern}': {count} 次")
    
    return frequent_patterns

def try_character_frequency_analysis(content):
    """基于字符频率的替换尝试"""
    print("进行字符频率分析...")
    
    # 统计字符频率
    char_freq = {}
    for char in content:
        if char.isalnum() or char in "!@#$%^&*()_+-=[]{}|;:,.<>?":
            char_freq[char] = char_freq.get(char, 0) + 1
    
    # 按频率排序
    sorted_chars = sorted(char_freq.items(), key=lambda x: x[1], reverse=True)
    
    # 英语字母频率（大致）
    english_freq = ['e', 't', 'a', 'o', 'i', 'n', 's', 'h', 'r', 'd', 'l', 'u']
    
    # 尝试基于频率的替换
    substitution_map = {}
    for i, (char, freq) in enumerate(sorted_chars[:len(english_freq)]):
        if char.isalpha():
            substitution_map[char] = english_freq[i % len(english_freq)]
    
    # 应用替换
    result = content
    for old_char, new_char in substitution_map.items():
        result = result.replace(old_char, new_char)
    
    print(f"频率分析替换结果预览: {result[:100]}...")
    return result

def try_pattern_based_decode(content):
    """基于模式的解码尝试"""
    print("尝试基于模式的解码...")
    
    results = []
    
    # 1. 尝试移除重复字符
    # 移除连续重复的字符
    deduplicated = re.sub(r'(.)\1+', r'\1', content)
    results.append(("去重复字符", deduplicated))
    
    # 2. 尝试提取特定模式
    # 提取可能的命令模式
    commands = re.findall(r'[a-zA-Z]{3,}', content)
    if commands:
        command_text = ' '.join(commands[:50])  # 取前50个
        results.append(("提取命令", command_text))
    
    # 3. 尝试数字替换
    # 将数字替换为字母
    digit_to_letter = {'0': 'o', '1': 'i', '2': 'z', '3': 'e', '4': 'a', '5': 's', '6': 'g', '7': 't', '8': 'b', '9': 'g'}
    digit_replaced = content
    for digit, letter in digit_to_letter.items():
        digit_replaced = digit_replaced.replace(digit, letter)
    results.append(("数字替换", digit_replaced))
    
    # 4. 尝试符号替换
    symbol_to_letter = {'*': ' ', '+': 'n', '-': 'r', '_': 'l', '=': 'e', '?': 'h', '{': '(', '}': ')', '[': '(', ']': ')'}
    symbol_replaced = content
    for symbol, letter in symbol_to_letter.items():
        symbol_replaced = symbol_replaced.replace(symbol, letter)
    results.append(("符号替换", symbol_replaced))
    
    # 5. 尝试大小写转换
    case_swapped = content.swapcase()
    results.append(("大小写转换", case_swapped))
    
    for name, result in results:
        print(f"{name} 结果预览: {result[:100]}...")
    
    return results

def try_advanced_decode(content):
    """尝试高级解码技术"""
    print("尝试高级解码技术...")
    
    results = []
    
    # 1. ROT13 和其他旋转密码
    def rot_n(text, n):
        result = ""
        for char in text:
            if char.isalpha():
                base = ord('A') if char.isupper() else ord('a')
                result += chr((ord(char) - base + n) % 26 + base)
            else:
                result += char
        return result
    
    for n in [1, 3, 5, 7, 13, 25]:
        rotated = rot_n(content, n)
        results.append((f"ROT{n}", rotated))
    
    # 2. 尝试反向字符串
    reversed_content = content[::-1]
    results.append(("反向", reversed_content))
    
    # 3. 尝试交替字符提取
    even_chars = content[::2]  # 偶数位置字符
    odd_chars = content[1::2]  # 奇数位置字符
    results.append(("偶数位字符", even_chars))
    results.append(("奇数位字符", odd_chars))
    
    # 4. 尝试块交换
    mid = len(content) // 2
    swapped = content[mid:] + content[:mid]
    results.append(("块交换", swapped))
    
    for name, result in results:
        print(f"{name} 结果预览: {result[:100]}...")
    
    return results

def search_for_keywords(content):
    """搜索关键词和模式"""
    print("搜索关键词和模式...")
    
    # 常见的恶意软件关键词
    malware_keywords = [
        'powershell', 'cmd', 'exec', 'eval', 'download', 'invoke', 'iex',
        'wget', 'curl', 'http', 'https', 'ftp', 'tcp', 'udp',
        'registry', 'hkey', 'run', 'startup', 'temp', 'appdata',
        'system32', 'windows', 'program', 'files', 'users',
        'administrator', 'password', 'credential', 'token',
        'encrypt', 'decrypt', 'base64', 'encode', 'decode',
        'shell', 'reverse', 'bind', 'connect', 'socket',
        'process', 'thread', 'service', 'task', 'schedule'
    ]
    
    found_keywords = []
    content_lower = content.lower()
    
    for keyword in malware_keywords:
        if keyword in content_lower:
            # 找到关键词的上下文
            index = content_lower.find(keyword)
            start = max(0, index - 20)
            end = min(len(content), index + len(keyword) + 20)
            context = content[start:end]
            found_keywords.append((keyword, context))
    
    if found_keywords:
        print("找到可疑关键词:")
        for keyword, context in found_keywords[:10]:
            print(f"  {keyword}: ...{context}...")
    else:
        print("未找到明显的恶意软件关键词")
    
    return found_keywords

def main():
    parser = argparse.ArgumentParser(description='第三阶段深度解密脚本')
    parser.add_argument('input_file', help='输入的第二阶段分析文件')
    parser.add_argument('-o', '--output', help='输出文件名', default='stage3_decrypted.txt')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("第三阶段深度解密分析")
    print("=" * 60)
    
    # 提取混淆内容
    content = extract_content_from_stage2(args.input_file)
    if not content:
        print("无法提取混淆内容")
        return
    
    print(f"混淆内容长度: {len(content)}")
    print(f"内容预览: {content[:200]}...")
    print("-" * 60)
    
    # 分析重复模式
    patterns = analyze_repeating_patterns(content)
    print("-" * 60)
    
    # 字符频率分析
    freq_result = try_character_frequency_analysis(content)
    print("-" * 60)
    
    # 基于模式的解码
    pattern_results = try_pattern_based_decode(content)
    print("-" * 60)
    
    # 高级解码技术
    advanced_results = try_advanced_decode(content)
    print("-" * 60)
    
    # 搜索关键词
    keywords = search_for_keywords(content)
    print("-" * 60)
    
    # 保存所有结果
    with open(args.output, 'w', encoding='utf-8') as f:
        f.write("第三阶段深度解密分析结果\n")
        f.write("=" * 60 + "\n\n")
        
        f.write("原始混淆内容:\n")
        f.write(content + "\n\n")
        
        f.write("重复模式分析:\n")
        for pattern, count in sorted(patterns.items(), key=lambda x: x[1], reverse=True)[:20]:
            f.write(f"'{pattern}': {count} 次\n")
        f.write("\n")
        
        f.write("字符频率分析结果:\n")
        f.write(freq_result + "\n\n")
        
        f.write("基于模式的解码尝试:\n")
        for name, result in pattern_results:
            f.write(f"{name}:\n{result}\n\n")
        
        f.write("高级解码技术尝试:\n")
        for name, result in advanced_results:
            f.write(f"{name}:\n{result[:1000]}...\n\n")  # 限制长度
        
        f.write("找到的关键词:\n")
        for keyword, context in keywords:
            f.write(f"{keyword}: {context}\n")
        f.write("\n")
    
    print(f"深度分析结果已保存到: {args.output}")

if __name__ == "__main__":
    main()
